#!/bin/bash

# Consolidated script to download ALL required JAR files for Spark Metadata-Driven Pipeline
# Includes: AWS SDK, Database drivers, Apache Iceberg, and Hadoop dependencies

set -e

# Create jars directory
JARS_DIR="src/main/resources/jars"
mkdir -p "$JARS_DIR"

echo "🔽 Downloading JAR files to $JARS_DIR..."
echo ""

cd "$JARS_DIR"

# AWS SDK and Hadoop S3A support
echo "📦 Downloading AWS and Hadoop JARs..."
curl -L -O https://repo1.maven.org/maven2/org/apache/hadoop/hadoop-aws/3.3.4/hadoop-aws-3.3.4.jar
curl -L -O https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.12.262/aws-java-sdk-bundle-1.12.262.jar

# Database drivers
echo "🗄️  Downloading database drivers..."
curl -L -O https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar
curl -L -O https://repo1.maven.org/maven2/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar
curl -L -O https://repo1.maven.org/maven2/com/microsoft/sqlserver/mssql-jdbc/12.4.1.jre11/mssql-jdbc-12.4.1.jre11.jar

# Note: Iceberg JARs not needed for simple Parquet output

echo ""
echo "✅ All JAR files downloaded successfully!"
echo ""
echo "📋 Files in $JARS_DIR:"
ls -la

cd - > /dev/null

echo ""
echo "🎉 Setup complete! You now have all dependencies for:"
echo "   • PostgreSQL, MySQL, SQL Server database connections"
echo "   • S3 file storage (LocalStack compatible)"
echo "   • Simple Parquet file output"
