#!/usr/bin/env python3
"""
Simple script to show Parquet data content
"""

import sys
import os

# Add src/main to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'main'))

from config.spark_session_builder import SparkSessionBuilder, setup_environment


def show_data():
    """Show Parquet data as clean dataframes"""
    
    print("📊 Your Parquet Data:")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Create Spark session
    spark = SparkSessionBuilder.create_iceberg_session("DataViewer", "WARN")
    
    # Check data
    tables = [
        ("MySQL Customers", "s3a://mock-bucket/mysql_customers/"),
        ("SQL Server Customers", "s3a://mock-bucket/mssql_customers/")
    ]
    
    for name, path in tables:
        try:
            df = spark.read.parquet(path)
            count = df.count()
            
            if count > 0:
                print(f"\n🗄️  {name} ({count} records)")
                print("-" * 40)
                df.show(10, truncate=False)
            else:
                print(f"\n⚠️  {name}: No data found")
                
        except Exception as e:
            if "Path does not exist" in str(e):
                print(f"\n❌ {name}: No files created yet")
            else:
                print(f"\n❌ {name}: Error - {e}")
    
    spark.stop()
    print("\n✅ This is your data from databases saved as Parquet files!")


if __name__ == "__main__":
    show_data()
