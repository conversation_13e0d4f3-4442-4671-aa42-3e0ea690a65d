jobs:
  # Job 1: MySQL to Parquet Files
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns:
           - id
           - name
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      mode: append
      partition_columns:
        - age_range
      format:
        type: IcebergWriterFormat
        source_options:
          compression: snappy

  # Job 2: SQL Server to Parquet Files
  - name: mssql_to_parquet
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: age_range
             expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
       - type: ProjectionTransformer
         columns:
           - id
           - name
           - age
           - age_range
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mssql_customers
      mode: append
      partition_columns:
        - age_range
      format:
        type: IcebergWriterFormat
        source_options:
          compression: snappy


