from config.transformer import TransformerConfig,ProjectionTransformerConfig,WithColumnTransformerConfig
from transformer.transformer import Transformer
from transformer.projection_transformer import ProjectionTransformer
from transformer.withcolumn_transformer import WithColumnTransformer
from typing import Optional

class TransfomerFactory:
    @staticmethod
    def create(transformer_configs: Optional[list[TransformerConfig]], custom_options: dict[str,str]) -> list[Transformer]:
        list_transformer = []
        if transformer_configs is not None:
            for trans_conf in transformer_configs:
                if isinstance(trans_conf, ProjectionTransformerConfig):
                    transformer = ProjectionTransformer(trans_conf)
                elif isinstance(trans_conf,WithColumnTransformerConfig):
                    transformer = WithColumnTransformer(trans_conf)
                else:
                        raise ValueError("Unsupported type of Transformer")
                list_transformer.append(transformer) # type: ignore
        return list_transformer