from typing import Union, Optional
# ,DSVReaderFormatConfig,ParquetReaderFormatConfig,AvroReaderFormatConfig,XMLReaderFormatConfig,ORCReaderFormatConfig,ExcelReaderFormatConfig,JsonReaderFormatConfig
from config.reader import MysqlReaderConfig, MssqlReaderConfig, PostgresReaderConfig, S3ReaderConfig
from config.transformer import ProjectionTransformerConfig, WithColumnTransformerConfig
# ,DSVWriterFormatConfig,ParquetWriterFormatConfig,AvroWriterFormatConfig,XMLWriterFormatConfig,ORCWriterFormatConfig,ExcelWriterFormatConfig,JsonlWriterFormatConfig
from config.writer import PostgresWriterConfig, S3WriterConfig, IcebergWriterConfig
from dataclasses import dataclass
from transformer.transformer import Transformer
from reader.reader import Reader
from writer.writer import Writer


READER_CONFIG_UNION = Union[MysqlReaderConfig,
                            MssqlReaderConfig, PostgresReaderConfig, S3ReaderConfig]
TRANSFORMER_CONFIG_UNION = Union[ProjectionTransformerConfig,
                                 WithColumnTransformerConfig]
WRITER_CONFIG_UNION = Union[PostgresWriterConfig, S3WriterConfig]


@dataclass
class JobParams:
    config_file_path: str
    config_source_type: str
    config_root_dir: str
    app_type: str
    lower_bound: str
    upper_bound: str
    report_date: str
    pipeline_name: list[str]
    batch_size: int
    custom: dict[str, str]
    predicate: str
    s3_type: str
    job_id: str
    s3_url: str = ""
    extra_jars_folder: str = "/opt/spark/jars"


@dataclass
class Pipeline:
    readers: Reader
    transformers: Optional[list[Transformer]]
    writers: Writer
