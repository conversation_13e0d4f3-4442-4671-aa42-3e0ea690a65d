import os
import boto3
from urllib.parse import urlparse
from botocore.exceptions import ClientError
import logging
from config.environment import env_config

logger = logging.getLogger(__name__)


def read_file(file_path: str) -> str:
    """
    Read the content of a file from local filesystem or S3.

    Args:
        file_path (str): The path to the file (local path or s3://bucket/key)

    Returns:
        str: The content of the file as a string.

    Raises:
        FileNotFoundError: If the file does not exist.
        IOError: If there's an error reading the file.
    """
    logger.info(f"Reading file {file_path}")

    if file_path.startswith('s3://'):
        return _read_s3_file(file_path)
    else:
        return _read_local_file(file_path)


def _read_local_file(file_path: str) -> str:
    """Read file from local filesystem"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        logger.error(f"Error: The file {file_path} does not exist.")
        raise FileNotFoundError(f"File not found: {file_path}")
    except PermissionError:
        logger.error(
            f"Error: You don't have permission to access {file_path}.")
        raise IOError(f"Permission denied: {file_path}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        raise IOError(f"Error reading file {file_path}: {e}")


def _read_s3_file(s3_path: str) -> str:
    """Read file from S3"""
    try:
        parsed_url = urlparse(s3_path)
        bucket = parsed_url.netloc
        key = parsed_url.path.lstrip('/')

        # Create S3 client with appropriate configuration
        s3_client_config = {}
        if env_config.s3_endpoint:
            s3_client_config['endpoint_url'] = env_config.s3_endpoint

        s3_client = boto3.client('s3',
                                 region_name=env_config.aws_region,
                                 **s3_client_config)

        response = s3_client.get_object(Bucket=bucket, Key=key)
        return response['Body'].read().decode('utf-8')

    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchKey':
            raise FileNotFoundError(f"S3 file not found: {s3_path}")
        elif error_code == 'NoSuchBucket':
            raise FileNotFoundError(f"S3 bucket not found: {bucket}")
        else:
            raise IOError(f"Error reading S3 file {s3_path}: {e}")
    except Exception as e:
        raise IOError(f"Error reading S3 file {s3_path}: {e}")


def write_file(file_path: str, content: str) -> None:
    """
    Write content to a file (local or S3).

    Args:
        file_path (str): The path to the file (local path or s3://bucket/key)
        content (str): The content to write
    """
    if file_path.startswith('s3://'):
        _write_s3_file(file_path, content)
    else:
        _write_local_file(file_path, content)


def _write_local_file(file_path: str, content: str) -> None:
    """Write file to local filesystem"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
    except IOError as e:
        raise IOError(f"Error writing file {file_path}: {e}")


def _write_s3_file(s3_path: str, content: str) -> None:
    """Write file to S3"""
    try:
        parsed_url = urlparse(s3_path)
        bucket = parsed_url.netloc
        key = parsed_url.path.lstrip('/')

        # Create S3 client with appropriate configuration
        s3_client_config = {}
        if env_config.s3_endpoint:
            s3_client_config['endpoint_url'] = env_config.s3_endpoint

        s3_client = boto3.client('s3',
                                 region_name=env_config.aws_region,
                                 **s3_client_config)

        s3_client.put_object(Bucket=bucket, Key=key,
                             Body=content.encode('utf-8'))

    except ClientError as e:
        raise IOError(f"Error writing S3 file {s3_path}: {e}")
    except Exception as e:
        raise IOError(f"Error writing S3 file {s3_path}: {e}")
