"""
AWS Secrets Manager integration for database credentials
"""
import json
import boto3
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
import logging
from config.environment import env_config

logger = logging.getLogger(__name__)


class SecretsManager:
    """AWS Secrets Manager client for retrieving database credentials"""
    
    def __init__(self, region_name: Optional[str] = None):
        self.region_name = region_name or env_config.db_secrets_region
        self.client = boto3.client('secretsmanager', region_name=self.region_name)
    
    def get_secret(self, secret_name: str) -> Dict[str, Any]:
        """
        Retrieve a secret from AWS Secrets Manager
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            
        Returns:
            Dictionary containing the secret values
            
        Raises:
            ClientError: If secret cannot be retrieved
        """
        try:
            response = self.client.get_secret_value(SecretId=secret_name)
            secret_string = response['SecretString']
            return json.loads(secret_string)
        except ClientError as e:
            logger.error(f"Error retrieving secret {secret_name}: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing secret {secret_name}: {e}")
            raise
    
    def get_database_credentials(self, secret_name: str) -> Dict[str, str]:
        """
        Get database credentials from AWS Secrets Manager
        
        Expected secret format:
        {
            "username": "db_user",
            "password": "db_password",
            "host": "db_host",
            "port": 5432,
            "database": "db_name"
        }
        
        Args:
            secret_name: Name of the database secret
            
        Returns:
            Dictionary with database connection parameters
        """
        secret = self.get_secret(secret_name)
        
        # Validate required fields
        required_fields = ['username', 'password']
        for field in required_fields:
            if field not in secret:
                raise ValueError(f"Required field '{field}' not found in secret {secret_name}")
        
        return {
            'username': secret['username'],
            'password': secret['password'],
            'host': secret.get('host', 'localhost'),
            'port': secret.get('port', 5432),
            'database': secret.get('database', ''),
            'schema': secret.get('schema', 'public')
        }


# Global secrets manager instance
secrets_manager = SecretsManager() if env_config.db_secrets_manager_enabled else None


def get_credentials_from_secret(secret_name: str) -> Optional[Dict[str, str]]:
    """
    Helper function to get credentials from secrets manager if enabled
    
    Args:
        secret_name: Name of the secret
        
    Returns:
        Credentials dictionary or None if secrets manager is disabled
    """
    if not env_config.db_secrets_manager_enabled or not secrets_manager:
        return None
    
    try:
        return secrets_manager.get_database_credentials(secret_name)
    except Exception as e:
        logger.warning(f"Failed to retrieve credentials from secrets manager: {e}")
        return None
