import pytest
from sqlalchemy import create_engine, text
from pyspark.sql import SparkSession
import boto3
import os

# MySQL Configuration
MYSQL_USER = "test"
MYSQL_PASSWORD = "123456"
MYSQL_HOST = "localhost"
MYSQL_PORT = 3306
MYSQL_DB = "test"
POSTGRE_USER = "postgres"
POSTGRE_PASSWORD = "123456"
POSTGRE_HOST = "localhost"
POSTGRE_PORT = 5432
POSTGRE_DB = "test"
MSSQL_USER = "sa"
MSSQL_PASSWORD = "123456aA"
MSSQL_HOST = "localhost"
MSSQL_PORT = "1433"
MSSQL_DATABASE = ""
MSSQL_DRIVER = "ODBC+Driver+18+for+SQL+Server"
MSSQL_DSN = "dsn"


DATA_STRING = """
    INSERT INTO customers (id, name, age, created_at, updated_at) VALUES ('11','Alice', 30,'2025-01-01 00:00:01','2025-01-01 00:10:01');
    INSERT INTO customers (id, name, age, created_at, updated_at) VALUES ('22','Jane', 35,'2025-02-01 00:00:01','2025-02-01 00:10:01');
    INSERT INTO customers (id, name, age, created_at, updated_at) VALUES ('33','John', 40,'2025-03-01 00:00:01','2025-03-01 00:10:01');
    INSERT INTO customers (id, name, age, created_at, updated_at) VALUES ('44','Tommy', 45,'2025-01-11 00:00:01','2025-01-11 00:10:01');
    INSERT INTO customers (id, name, age, created_at, updated_at) VALUES ('55','Ben', 50,'2025-01-21 00:00:01','2025-01-21 00:10:01');
"""


@pytest.fixture(scope="session")
def mysql_engine():
    """Fixture to set up a MySQL test database and return an SQLAlchemy engine."""
    db_url = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}"

    # Create database engine
    engine = create_engine(db_url, echo=True, isolation_level="AUTOCOMMIT")

    yield engine  # Provide engine to tests

    # Cleanup the engine
    engine.dispose()


@pytest.fixture(scope="function")
def mysql_setup(mysql_engine):

    # Create test table
    with mysql_engine.connect() as conn:
        conn.execute(text(
            "CREATE TABLE IF NOT EXISTS customers (id int, name VARCHAR(50), age INT, created_at datetime, updated_at datetime)"))
        for stmt in DATA_STRING.strip().split(";"):
            if stmt.strip():
                conn.execute(text(stmt))

    yield mysql_engine  # Provide engine to tests

    # Cleanup: Drop the test table
    with mysql_engine.connect() as conn:
        conn.execute(text(f"DROP TABLE IF EXISTS customers"))
        conn.close()


@pytest.fixture(scope="session")
def postgresql_engine():
    """Fixture to set up a MySQL test database and return an SQLAlchemy engine."""
    db_url = f"postgresql+psycopg://{POSTGRE_USER}:{POSTGRE_PASSWORD}@{POSTGRE_HOST}:{POSTGRE_PORT}/{POSTGRE_DB}"

    # Create database engine
    engine = create_engine(db_url, echo=True, isolation_level="AUTOCOMMIT")

    yield engine  # Provide engine to tests

    # Cleanup the engine
    engine.dispose()


@pytest.fixture(scope="session")
def postgresql_create_control_table(postgresql_engine):
    sql_string_create = """
        CREATE TABLE IF NOT EXISTS job_logger (id SERIAL PRIMARY KEY NOT NULL, system_name VARCHAR(50), schema_name VARCHAR(50), table_name VARCHAR(50), lower_bound timestamp, upper_bound timestamp, record_count INT, created_at timestamp default current_timestamp, updated_at timestamp default current_timestamp)
    """
    sql_string_drop = """
        DROP TABLE IF EXISTS job_logger;
    """
    # Create test table
    with postgresql_engine.connect() as conn:
        conn.execute(text(sql_string_create))

    yield postgresql_engine  # Provide engine to tests

    # Cleanup: Drop the test table
    with postgresql_engine.connect() as conn:
        conn.execute(text(sql_string_drop))
        conn.close()


@pytest.fixture(scope="session")
def postgresql_insert_control_table(postgresql_engine):
    sql_string_create = """
        CREATE TABLE IF NOT EXISTS job_logger (id SERIAL PRIMARY KEY NOT NULL, system_name VARCHAR(50), schema_name VARCHAR(50), table_name VARCHAR(50), lower_bound timestamp, upper_bound timestamp, record_count INT, created_at timestamp default current_timestamp, updated_at timestamp default current_timestamp);
        INSERT INTO job_logger(system_name,schema_name,table_name,lower_bound,upper_bound,record_count,created_at,updated_at) VALUES('system_name', 'schema_name', 'table_name', '2025-01-01 00:00:00', '2025-01-01 00:15:00', 100, '2025-01-01 00:15:00', '2025-01-01 00:15:00')
    """
    # Create test table
    with postgresql_engine.connect() as conn:
        conn.execute(text(sql_string_create))

    yield postgresql_engine  # Provide engine to tests

    sql_string_drop = """
        DROP TABLE IF EXISTS job_logger;
    """
    # Cleanup: Drop the test table
    with postgresql_engine.connect() as conn:
        conn.execute(text(sql_string_drop))
        conn.close()


@pytest.fixture(scope="session")
def mssql_engine():
    """Fixture to set up a MSSQL test database and return an SQLAlchemy engine."""
    # Connect directly to the server instead of using DSN
    db_url = f"mssql+pyodbc://{MSSQL_USER}:{MSSQL_PASSWORD}@{MSSQL_HOST}:{MSSQL_PORT}/master?driver={MSSQL_DRIVER}&TrustServerCertificate=yes"

    # Create database engine
    engine = create_engine(db_url, echo=True, isolation_level="AUTOCOMMIT")

    yield engine  # Provide engine to tests

    # Cleanup the engine
    engine.dispose()


@pytest.fixture(scope="session")
def mssql_setup(mssql_engine):

    # First create the database
    sql_create_db = "CREATE DATABASE test;"
    # Then create the table in the test database
    sql_create_table = """
        USE test;
        CREATE TABLE customers (id int, name VARCHAR(50), age INT, created_at datetime, updated_at datetime);
    """
    with mssql_engine.connect() as conn:
        # Create database first
        conn.execute(text(sql_create_db))
        # Create table in the test database
        conn.execute(text(sql_create_table))
        # Insert test data using the test database
        for stmt in DATA_STRING.strip().split(";"):
            if stmt.strip():
                conn.execute(text(f"USE test; {stmt}"))

    yield mssql_engine  # Provide engine to tests

    # Cleanup: Drop the test table and database
    with mssql_engine.connect() as conn:
        conn.execute(text("USE test; DROP TABLE customers"))
        conn.execute(text("USE master; DROP DATABASE test"))
        conn.close()


AWS_ACCESS_KEY_ID = "test"
AWS_SECRET_ACCESS_KEY = "test"
AWS_REGION = "us-east-1"
LOCALSTACK_ENDPOINT = "http://localhost:4566"


@pytest.fixture(scope="session")
def s3_localstack():
    """Fixture to connect to LocalStack S3 with access key & secret"""
    os.environ["AWS_ACCESS_KEY_ID"] = AWS_ACCESS_KEY_ID
    os.environ["AWS_SECRET_ACCESS_KEY"] = AWS_SECRET_ACCESS_KEY
    os.environ["AWS_DEFAULT_REGION"] = AWS_REGION
    print("creating s3 bucket")
    # Create S3 client with LocalStack endpoint
    s3 = boto3.client(
        "s3",
        region_name=AWS_REGION,
        endpoint_url=LOCALSTACK_ENDPOINT,
    )

    bucket_name = "mock-bucket"
    s3.create_bucket(Bucket=bucket_name)

    yield s3, bucket_name  # Provide mock S3 client and bucket name

    # Cleanup
    del os.environ["AWS_ACCESS_KEY_ID"]
    del os.environ["AWS_SECRET_ACCESS_KEY"]
    del os.environ["AWS_DEFAULT_REGION"]


@pytest.fixture(scope="session")
def s3_mock_csv(s3_localstack):
    s3, bucket_name = s3_localstack
    csv_content = "name,age,city\nAlice,30,New York\nBob,25,Los Angeles"
    # Upload CSV file
    s3.put_object(Bucket=bucket_name, Key="raw/test.csv", Body=csv_content)
    print("uploaded csv")
    yield s3, bucket_name


@pytest.fixture(scope="session")
def spark_setup():
    """Fixture to initialize a SparkSession."""
    spark = SparkSession.Builder() \
        .appName("pytest-spark") \
        .master("local[*]") \
        .config("spark.driver.memory", "2g") \
        .config("spark.driver.extraClassPath", "src/main/resources/jars/*") \
        .config("spark.executor.extraClassPath", "src/main/resources/jars/*") \
        .config("spark.hadoop.fs.s3a.endpoint", LOCALSTACK_ENDPOINT) \
        .config("spark.hadoop.fs.s3a.connection.timeout", "5000") \
        .config("spark.hadoop.fs.s3a.attempts.maximum", "3") \
        .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem") \
        .config("spark.hadoop.fs.s3a.access.key", AWS_ACCESS_KEY_ID) \
        .config("spark.hadoop.fs.s3a.secret.key", AWS_SECRET_ACCESS_KEY) \
        .config("spark.hadoop.fs.s3a.endpoint.region", AWS_REGION) \
        .config("spark.hadoop.fs.s3a.path.style.access", "true") \
        .getOrCreate()

    yield spark  # Provide SparkSession to tests

    spark.stop()  # Cleanup after tests
