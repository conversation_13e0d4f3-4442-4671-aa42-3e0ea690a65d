import pytest
from config.reader import MssqlReaderConfig
from reader.mssql_reader import MssqlReader
import os

internal_endpoint= "src/test/resources/internal.endpoint.yaml"

class TestMysqlReader:
    def test_spark_mysql_full(self, mssql_setup, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        endpoint = "mssql.endpoint.yaml"
        config =MssqlReaderConfig(type="MssqlReader",endpoint=endpoint,table="customers",strategy="full")
        reader = MssqlReader(config, None,internal_endpoint)
        df = reader.read(spark_setup)
        assert df.count() == 5
        
    def test_spark_mssql_predicate(self, mssql_setup, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        endpoint = "mssql.endpoint.yaml"
        config =MssqlReaderConfig(type="MssqlReader",endpoint=endpoint,table="customers",strategy="full")
        predicate_dict = {"predicate":"id=11"}
        reader = MssqlReader(config, predicate_dict,internal_endpoint)
        df = reader.read(spark_setup)
        assert df.count() == 1  