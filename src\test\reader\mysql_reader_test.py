import pytest
from config.reader import MysqlReaderConfig
from reader.mysql_reader import MysqlReader
import os

internal_endpoint= "src/test/resources/internal.endpoint.yaml"

class TestMysqlReader:
    def test_spark_mysql_full(self, mysql_setup, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        endpoint = "mysql.endpoint.yaml"
        config = MysqlReaderConfig(type="MysqlReader",endpoint=endpoint,table="customers",strategy="full")
        reader = MysqlReader(config, None,internal_endpoint)
        df = reader.read(spark_setup)
        assert df.count() == 5
        
    def test_spark_mysql_predicate(self, mysql_setup, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        endpoint = "mysql.endpoint.yaml"
        config = MysqlReaderConfig(type="MysqlReader",endpoint=endpoint,table="customers",strategy="full")
        predicate_dict = {"predicate":"id=11"}
        reader = MysqlReader(config, predicate_dict,internal_endpoint)
        df = reader.read(spark_setup)
        assert df.count() == 1  