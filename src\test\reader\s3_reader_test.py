import pytest
from config.reader import S3<PERSON><PERSON>erConfig,DSVReaderFormatConfig
from reader.s3_reader import S3<PERSON>eader
from typing import Literal
import os

internal_endpoint= "src/test/resources/internal.endpoint.yaml"

class TestMysqlReader:
    def test_spark_s3_full(self, s3_mock_csv, spark_setup):
        os.environ["CONFIG_ENDPOINTS_DIR"] = "src/test/resources"
        endpoint = "s3.localstack.endpoint.yaml"
        format_config = DSVReaderFormatConfig(type="DSVReaderFormat")
        config = S3ReaderConfig(type="S3Reader",endpoint=endpoint,key="raw/test.csv",format=format_config)         # type: ignore
        print(config.format)
        reader = S3Reader(config,None)
        print(reader)
        df = reader.read(spark_setup)
        assert df.count() == 3
        
    